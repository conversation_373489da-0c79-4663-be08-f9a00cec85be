## 1. Overview

This program is a **personal alpha generation and simulation system** built with Python and PostgreSQL.
It consists of:

* **Session management** (biometric login + Telegram bot notification)
* **Alpha space parser** (generate alphas from templates & parameter space, save to DB)
* **Simulator** (submit alphas to external API, track status)
* **Result fetcher** (poll for completed results, save to DB)

It is designed for **single-user operation**, **continuous simulation**, and **efficient batching/threading**.

---

## 2. Goals

* Automate alpha signal generation from templates.
* Persist simulation lifecycle in PostgreSQL.
* Prevent duplicate re-simulations of alphas already in DB.
* Provide resilient simulation loop (simulator + fetcher).

---

## 3. Non-Goals

* Multi-user authentication.
* UI/dashboard (interface for viewing results will be separate component).
* Error alpha retry (errors remain until manual inspection).

---

## 4. Components

### 4.1 Session Management

The **session management module** is responsible for:

* **Authenticating with the external API** (standard login + biometric/persona flow, two step).
* **Managing session lifecycle** (token storage, expiry handling, refresh).
* **Providing session tokens** to downstream modules (simulator & fetcher).
* **Notifying and interacting with the user via Telegram bot**.

#### Authentication Flow

1. **Initial login attempt**

   * Attempt authentication with API.
   * If API responds with `401 Unauthorized` and header `WWW-Authenticate: persona`:

     * Request a **persona login link** (biometric link).
     * Send the link to the user via Telegram bot.

2. **User confirmation**

   * User clicks the link and completes biometric authentication and click Complete buttun then resend according to @doc/API_Document.md.
   * On `/login`, system regenerates a new persona link and sends it.

3. **Session token storage**

   * On successful login, store session token securely on disk (JSON file with token + expiry timestamp).
   * All API requests automatically use this stored token.

---

#### Session Lifecycle

* **Token refresh**

  * Before expiry, system attempts auto-refresh if API allows.
  * If refresh fails → wait for user to use `/login`.

* **Expiry notifications (Telegram bot)**

  * Notify user at:

    * **1 hour before expiry**
    * **30 minutes before expiry**
    * **On expiry** (prompt user to run `/login`).

* **Failure handling**

  * On repeated `401 Unauthorized` during API calls:

    * Assume token invalid.
    * Restart login flow.
    * Send Telegram alert for manual intervention and relogin.

---

#### Telegram Bot Worker

A **continuous worker process** runs a Telegram bot that interacts with the user.
It listens for commands and bridges manual actions to the session manager.

**Supported Commands**

1. `/login`

   * Triggers a new authentication attempt.
   * If API requires persona, bot requests link and sends it.
   * Waits for biometric confirmation (user press button).
   * On success: session refreshed + bot confirms.

2. `/status`

   * Displays current session status:

     * Active/Expired
     * Remaining time until expiry (HH\:MM).

3. `/help`

   * Lists available commands (`/login`, `/status`, `/help`).

---

#### Responsibilities

* Expose shared **session object** (for simulator & fetcher):

  * `get_session()` → returns valid token or triggers login flow.
  * `is_expired()` → checks token expiry.
* Expose Telegram helper:

  * `notify_tg(message)` → send custom notifications.


---

### 4.2 Alpha Space Parser (Single-run script)

* Input:

  * Template (Python snippet with placeholders)
  * Parameter space (dictionary of substitutions)
  * Settings (universe, region, delay, etc.)
* Output:

  * All possible alpha code variants (batched to avoid explosion).
* Store:

  * Template → `template` table.
  * Alpha code → `alpha` table (`status=none`).
  * Reference mapping → `alpha_reference` table.
* Deduplication:

  * Skip alpha if identical code already exists (no hash, exact text compare).
* **Future TODO (not v1):**

  * Cleanup parser output by stripping comments, redundant newlines, extra spaces.
  * This ensures alpha simplicity and prevents duplicates caused by formatting.

---

### 4.3 Simulator (Continuous loop)

* Fetch `alpha` rows with `status=none`.
* Submit in batches with threading (respect API concurrency limits).
* Update `status=simulating` and record `simulation_id`.
* If API fails → set `status=error` and save error message.
* **No retries for error alphas.**

---

### 4.4 Result Fetcher (Continuous loop)

* Fetch `alpha` rows with `status=simulating`.
* Poll API using `simulation_id`.
* On completion:

  * Update `status=finish`.
  * Store `result` and `result_at`.
* On error:

  * Save error and mark `status=error`.

---

## 5. Database Schema

### `template`

| Field       | Type      | Notes           |
| ----------- | --------- | --------------- |
| id          | SERIAL PK |                 |
| template    | TEXT      | Template string |
| created\_at | TIMESTAMP | default now()   |

### `alpha`

| Field        | Type      | Notes                                   |
| ------------ | --------- | --------------------------------------- |
| id           | SERIAL PK |                                         |
| alpha        | TEXT      | generated alpha code                    |
| status       | ENUM      | `none`, `simulating`, `finish`, `error` |
| simulationid | TEXT      | external API simulation ID              |
| result       | JSONB     | simulation results                      |
| error        | TEXT      | error message                           |
| created\_at  | TIMESTAMP | default now()                           |
| simulate\_at | TIMESTAMP | set when sent to simulator              |
| result\_at   | TIMESTAMP | set when results retrieved              |

### `alpha_reference`

| Field        | Type      | Notes               |
| ------------ | --------- | ------------------- |
| id           | SERIAL PK |                     |
| alpha\_id    | INT       | FK → `alpha(id)`    |
| template\_id | INT       | FK → `template(id)` |

---

## 6. Threading & Batching

* **Parser:** generate in batches to avoid memory blowup.
* **Simulator:** thread pool, configurable concurrency (e.g. 5–10 threads).
* **Fetcher:** threaded polling with backoff.

---

## 7. Success Metrics

* Parser generates N alphas without OOM (tested in batches).
* Simulator handles API limits smoothly.
* No duplicate simulations (exact text match).
* Fetcher retrieves >95% results successfully.
* Error alphas clearly marked, not retried.

---

## 8. Risks & Mitigations

* **API concurrency limit** → handled with batching + throttling.
* **Parser explosion** → batching + optional pruning.
* **Session expiry** → auto-refresh with Telegram alert.
* **Error accumulation** → left for manual review (no retries).