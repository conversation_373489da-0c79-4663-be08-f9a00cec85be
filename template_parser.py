"""
Template parser for generating alpha variants from templates and parameter spaces.

This module implements the Alpha Space Parser component as described in the PRD.
It generates all possible alpha code variants from templates and parameter spaces,
saves them to the database with proper deduplication.
"""

import logging
import itertools
import json
from typing import Dict, List, Any, Tuple
from datetime import datetime

from tqdm import tqdm
from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

from db import get_database_engine

logger = logging.getLogger(__name__)


class TemplateParser:
    """
    Parses templates with parameter spaces to generate alpha variants.

    Handles:
    - String replacement to generate all permutations
    - Settings space expansion with defaults
    - Database storage with deduplication
    - Batch processing to avoid memory issues
    """

    # Default settings as specified in the API documentation
    DEFAULT_SETTINGS = {
        'instrumentType': 'EQUITY',
        'region': 'USA',
        'universe': 'TOP3000',
        'delay': 1,
        'decay': 15,
        'neutralization': 'SUBINDUSTRY',
        'truncation': 0.08,
        'maxTrade': 'ON',
        'pasteurization': 'ON',
        'testPeriod': 'P1Y6M',
        'unitHandling': 'VERIFY',
        'nanHandling': 'OFF',
        'language': 'FASTEXPR',
        'visualization': False,
    }

    def __init__(self, batch_size: int = 100):
        """
        Initialize the template parser.

        Args:
            batch_size: Number of alphas to process in each batch to avoid memory issues
        """
        self.batch_size = batch_size
        self.db_engine = get_database_engine()
        self.Session = sessionmaker(bind=self.db_engine)

    def parse_template(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> Tuple[int, int]:
        """
        Parse a template with parameter and settings spaces to generate alpha variants.

        Args:
            template: Template string with placeholders (e.g., "<data>", "<op>")
            parameter_space: Dictionary mapping placeholders to possible values
            settings_space: Dictionary mapping setting names to possible values

        Returns:
            Tuple of (template_id, number_of_alphas_generated)
        """
        # Calculate total expected combinations for progress bar
        param_combinations = 1
        for values in parameter_space.values():
            param_combinations *= len(values)

        settings_combinations = 1
        for values in settings_space.values():
            settings_combinations *= len(values)

        total_expected = param_combinations * settings_combinations

        print(f"🚀 Starting template parsing")
        print(f"📝 Template length: {len(template)} characters")
        print(f"🔧 Parameter space: {list(parameter_space.keys())} ({param_combinations} combinations)")
        print(f"⚙️  Settings space: {list(settings_space.keys())} ({settings_combinations} combinations)")
        print(f"📊 Total expected alphas: {total_expected:,}")

        # Save template to database
        template_id = self._save_template(template, parameter_space, settings_space)
        print(f"💾 Template saved with ID: {template_id}")

        # Generate all alpha variants in batches with progress bar
        total_generated = 0
        batch_count = 0

        # Create progress bar
        with tqdm(total=total_expected, desc="🔄 Processing alphas", unit="alpha") as pbar:
            for alpha_batch in self._generate_alpha_batches(template, parameter_space, settings_space):
                batch_count += 1
                batch_size = len(alpha_batch)

                generated_count = self._save_alpha_batch(alpha_batch, template_id)
                total_generated += generated_count

                # Update progress bar
                pbar.update(batch_size)
                pbar.set_postfix({
                    'batch': batch_count,
                    'saved': f"{generated_count}/{batch_size}",
                    'total_saved': total_generated
                })

        print(f"✅ Template parsing completed!")
        print(f"📈 Total alphas generated: {total_generated:,}")
        print(f"🎯 Deduplication rate: {((total_expected - total_generated) / total_expected * 100):.1f}%")
        return template_id, total_generated

    def _save_template(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> int:
        """Save template to database and return its ID."""
        with self.Session() as session:
            with session.begin():
                # Insert template with parameter_space and settings_space
                result = session.execute(
                    text("""
                        INSERT INTO template (template, parameter_space, settings_space, created_at)
                        VALUES (:template, CAST(:parameter_space AS jsonb), CAST(:settings_space AS jsonb), :created_at)
                        RETURNING id
                    """),
                    {
                        "template": template,
                        "parameter_space": json.dumps(parameter_space),
                        "settings_space": json.dumps(settings_space),
                        "created_at": datetime.now()
                    }
                )
                template_id = result.scalar()
                return template_id

    def _generate_alpha_batches(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ):
        """
        Generate alpha variants in batches to avoid memory explosion.

        Yields batches of (alpha_code, settings) tuples.
        """
        # Generate all parameter combinations
        param_keys = list(parameter_space.keys())
        param_values = [parameter_space[key] for key in param_keys]

        # Generate all settings combinations
        settings_keys = list(settings_space.keys())
        settings_values = [settings_space[key] for key in settings_keys]

        current_batch = []

        # Generate cartesian product of all combinations
        for param_combo in itertools.product(*param_values):
            # Create parameter substitution mapping
            param_mapping = dict(zip(param_keys, param_combo))

            # Generate alpha code by replacing placeholders
            alpha_code = template
            for placeholder, value in param_mapping.items():
                alpha_code = alpha_code.replace(placeholder, str(value))

            # Generate all settings combinations for this alpha
            for settings_combo in itertools.product(*settings_values):
                # Create full settings by merging with defaults
                full_settings = self.DEFAULT_SETTINGS.copy()
                settings_mapping = dict(zip(settings_keys, settings_combo))
                full_settings.update(settings_mapping)
                # print("---------")
                # print("alpha_code",alpha_code)
                # print("full_settings",full_settings)
                # print("---------")
                current_batch.append((alpha_code, full_settings))

                # Yield batch when it reaches the specified size
                if len(current_batch) >= self.batch_size:
                    yield current_batch
                    current_batch = []

        # Yield remaining items in the last batch
        if current_batch:
            yield current_batch

    def _save_alpha_batch(self, alpha_batch: List[Tuple[str, Dict]], template_id: int) -> int:
        """
        Save a batch of alphas to database with deduplication.

        Args:
            alpha_batch: List of (alpha_code, settings) tuples
            template_id: ID of the template these alphas were generated from

        Returns:
            Number of alphas actually saved (after deduplication)
        """
        saved_count = 0

        with self.Session() as session:
            with session.begin():
                for alpha_code, settings in alpha_batch:
                    # Check if alpha already exists (exact text match for deduplication)
                    existing = session.execute(
                        text("SELECT id FROM alpha WHERE alpha = :alpha_code AND settings = CAST(:settings AS jsonb)"),
                        {"alpha_code": alpha_code, "settings": json.dumps(settings)}
                    ).scalar()

                    if existing:
                        # Skip duplicate without logging (too verbose)
                        continue

                    # Insert new alpha
                    alpha_result = session.execute(
                        text("""
                            INSERT INTO alpha (alpha, settings, status, created_at)
                            VALUES (:alpha, CAST(:settings AS jsonb), 'none', :created_at)
                            RETURNING id
                        """),
                        {
                            "alpha": alpha_code,
                            "settings": json.dumps(settings),
                            "created_at": datetime.now()
                        }
                    )
                    alpha_id = alpha_result.scalar()

                    # Insert reference mapping
                    session.execute(
                        text("""
                            INSERT INTO alpha_reference (alpha_id, template_id)
                            VALUES (:alpha_id, :template_id)
                        """),
                        {
                            "alpha_id": alpha_id,
                            "template_id": template_id
                        }
                    )

                    saved_count += 1

        return saved_count


def parse_from_example_file(example_file_path: str = "alpha_space/example.py") -> Tuple[int, int]:
    """
    Parse template from the example file.

    Args:
        example_file_path: Path to the example file containing template, parameter_space, and settings_space

    Returns:
        Tuple of (template_id, number_of_alphas_generated)
    """
    # Import the example file to get template, parameter_space, and settings_space
    import importlib.util
    import sys

    spec = importlib.util.spec_from_file_location("example", example_file_path)
    example_module = importlib.util.module_from_spec(spec)
    sys.modules["example"] = example_module
    spec.loader.exec_module(example_module)

    template = example_module.template
    parameter_space = example_module.parameter_space
    settings_space = example_module.settings_space
    print("-----settingspace",parameter_space)

    parser = TemplateParser()
    return parser.parse_template(template, parameter_space, settings_space)


if __name__ == "__main__":
    """
    Main entry point for running the template parser.
    Can be run as a standalone script.
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        template_id, alpha_count = parse_from_example_file()
        print(f"Template parsing completed successfully!")
        print(f"Template ID: {template_id}")
        print(f"Alphas generated: {alpha_count}")
    except Exception as e:
        logger.error(f"Template parsing failed: {e}", exc_info=True)
        print(f"Error: {e}")
