"""
Test script for template_parser.py

This script tests the template parser functionality with a simple example.
"""

import logging
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from template_parser import TemplateParser

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def test_simple_template():
    """Test with a simple template to verify functionality."""

    # Simple test template
    template = """
    data=<data>;
    signal=<sign>group_neutralize(data, <group>);
    <op>(signal)
    """

    # Simple parameter space
    parameter_space = {
        "<data>": ["close", "volume"],
        "<sign>": ["", "-"],
        "<group>": ["sector", "industry"],
        "<op>": ["rank", "zscore"]
    }

    # Simple settings space
    settings_space = {
        'region': ['USA'],
        'universe': ['TOP3000', 'TOP1000'],
        'decay': [5, 15],
    }

    logger.info("Starting simple template test")
    logger.info(f"Expected combinations: {2 * 2 * 2 * 2 * 1 * 2 * 2} = {2 * 2 * 2 * 2 * 1 * 2 * 2}")

    try:
        parser = TemplateParser(batch_size=10)  # Small batch size for testing
        template_id, alpha_count = parser.parse_template(template, parameter_space, settings_space)

        logger.info(f"Test completed successfully!")
        logger.info(f"Template ID: {template_id}")
        logger.info(f"Alphas generated: {alpha_count}")

        return True

    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)
        return False


def test_example_file():
    """Test with the actual example file."""

    logger.info("Starting example file test")

    try:
        from template_parser import parse_from_example_file
        template_id, alpha_count = parse_from_example_file()

        logger.info(f"Example file test completed successfully!")
        logger.info(f"Template ID: {template_id}")
        logger.info(f"Alphas generated: {alpha_count}")

        return True

    except Exception as e:
        logger.error(f"Example file test failed: {e}", exc_info=True)
        return False


if __name__ == "__main__":
    """Run tests."""

    print("=" * 60)
    print("Testing Template Parser")
    print("=" * 60)

    # Test 1: Simple template
    print("\n1. Testing simple template...")
    success1 = test_simple_template()

    # Test 2: Example file
    # print("\n2. Testing example file...")
    # success2 = test_example_file()

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"Simple template test: {'PASSED' if success1 else 'FAILED'}")
    print(f"Example file test: {'PASSED' if success2 else 'FAILED'}")

    if success1 and success2:
        print("\nAll tests PASSED! ✅")
        sys.exit(0)
    else:
        print("\nSome tests FAILED! ❌")
        sys.exit(1)
