"""
Performance test for the optimized template parser.

This script tests different configurations to show the performance improvements
with threading and memory-efficient processing.
"""

import time
import logging
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from template_parser import TemplateParser

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_large_test_case():
    """Create a larger test case to demonstrate performance improvements."""
    
    template = """
data=<data>;
databf=ts_backfill(data, <bf_days>);
signal=<sign>group_neutralize(databf, <group>);
ts_signal=<ts_op>(signal, <op_days>);
final=<final_op>(ts_signal, <final_param>);
<output_op>(final)
"""
    
    parameter_space = {
        "<data>": ["close", "volume", "high", "low"],
        "<sign>": ["", "-"],
        "<bf_days>": ["21", "63", "126"],
        "<op_days>": ["1", "5", "21"],
        "<group>": ["sector", "industry"],
        "<ts_op>": ["ts_delta", "ts_zscore"],
        "<final_op>": ["rank", "zscore"],
        "<final_param>": ["1", "5"],
        "<output_op>": ["rank", "zscore", "windsorize"]
    }
    
    settings_space = {
        'region': ['USA'],
        'universe': ['TOP3000', 'TOP1000'],
        'decay': [5, 15],
        'neutralization': ['SECTOR', 'SUBINDUSTRY'],
    }
    
    # Calculate expected combinations
    param_combinations = 1
    for values in parameter_space.values():
        param_combinations *= len(values)
    
    settings_combinations = 1
    for values in settings_space.values():
        settings_combinations *= len(values)
    
    total_expected = param_combinations * settings_combinations
    
    return template, parameter_space, settings_space, total_expected


def test_configuration(name: str, batch_size: int, max_workers: int):
    """Test a specific configuration and measure performance."""
    
    print(f"\n{'='*60}")
    print(f"🧪 Testing Configuration: {name}")
    print(f"📦 Batch size: {batch_size}")
    print(f"🧵 Max workers: {max_workers}")
    print(f"{'='*60}")
    
    template, parameter_space, settings_space, total_expected = create_large_test_case()
    
    print(f"📊 Expected total alphas: {total_expected:,}")
    
    start_time = time.time()
    
    try:
        parser = TemplateParser(batch_size=batch_size, max_workers=max_workers)
        template_id, alpha_count = parser.parse_template(template, parameter_space, settings_space)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ {name} Results:")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"📈 Alphas generated: {alpha_count:,}")
        print(f"🚀 Throughput: {alpha_count/duration:.1f} alphas/second")
        print(f"💾 Template ID: {template_id}")
        
        return {
            'name': name,
            'duration': duration,
            'alpha_count': alpha_count,
            'throughput': alpha_count/duration,
            'batch_size': batch_size,
            'max_workers': max_workers
        }
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n❌ {name} Failed:")
        print(f"⏱️  Duration before failure: {duration:.2f} seconds")
        print(f"🚨 Error: {e}")
        
        return {
            'name': name,
            'duration': duration,
            'alpha_count': 0,
            'throughput': 0,
            'batch_size': batch_size,
            'max_workers': max_workers,
            'error': str(e)
        }


def main():
    """Run performance tests with different configurations."""
    
    print("🚀 Template Parser Performance Test")
    print("=" * 60)
    
    # Test different configurations
    configurations = [
        ("Single Thread, Small Batch", 100, 1),
        ("Single Thread, Large Batch", 1000, 1),
        ("Multi Thread, Small Batch", 100, 4),
        ("Multi Thread, Large Batch", 1000, 4),
        ("High Concurrency", 2000, 8),
    ]
    
    results = []
    
    for name, batch_size, max_workers in configurations:
        result = test_configuration(name, batch_size, max_workers)
        results.append(result)
        
        # Small delay between tests
        time.sleep(2)
    
    # Summary
    print(f"\n{'='*80}")
    print("📊 PERFORMANCE SUMMARY")
    print(f"{'='*80}")
    
    print(f"{'Configuration':<25} {'Duration':<10} {'Alphas':<10} {'Throughput':<15} {'Batch':<8} {'Workers':<8}")
    print("-" * 80)
    
    for result in results:
        if 'error' not in result:
            print(f"{result['name']:<25} {result['duration']:<10.2f} {result['alpha_count']:<10,} {result['throughput']:<15.1f} {result['batch_size']:<8} {result['max_workers']:<8}")
        else:
            print(f"{result['name']:<25} {'FAILED':<10} {'N/A':<10} {'N/A':<15} {result['batch_size']:<8} {result['max_workers']:<8}")
    
    # Find best configuration
    successful_results = [r for r in results if 'error' not in r and r['alpha_count'] > 0]
    if successful_results:
        best_result = max(successful_results, key=lambda x: x['throughput'])
        print(f"\n🏆 Best Configuration: {best_result['name']}")
        print(f"⚡ Best Throughput: {best_result['throughput']:.1f} alphas/second")
        print(f"📦 Optimal Batch Size: {best_result['batch_size']}")
        print(f"🧵 Optimal Workers: {best_result['max_workers']}")


if __name__ == "__main__":
    main()
