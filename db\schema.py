"""
Database schema initialization for BrainSpace.
"""

import logging
from sqlalchemy import text
from .connection import get_database_engine

logger = logging.getLogger(__name__)


def init_database_schema():
    """Initializes the database schema if tables do not exist."""
    engine = get_database_engine()
    with engine.connect() as connection:
        # Check if the ENUM type already exists before creating it
        enum_exists = connection.execute(text("""
            SELECT 1 FROM pg_type WHERE typname = 'alpha_status';
        """)).scalar()

        if not enum_exists:
            # Use autocommit for CREATE TYPE for wider compatibility
            autocommit_connection = connection.execution_options(isolation_level="AUTOCOMMIT")
            autocommit_connection.execute(text("""
                CREATE TYPE alpha_status AS ENUM ('none', 'simulating', 'finish', 'error');
            """))
            logger.info("Created ENUM type 'alpha_status'.")
        else:
            logger.info(
                "ENUM type 'alpha_status' already exists, skipping creation.")

    # Use a single transaction for all table and index creations
    with engine.begin() as connection:
        connection.execute(text("""
            CREATE TABLE IF NOT EXISTS template (
                id SERIAL PRIMARY KEY,
                template TEXT NOT NULL,
                parameter_space JSONB,
                settings_space JSONB,
                created_at TIMESTAMP DEFAULT NOW()
            );
        """))
        connection.execute(text("""
            CREATE TABLE IF NOT EXISTS alpha (
                id SERIAL PRIMARY KEY,
                alpha TEXT NOT NULL,
                settings JSONB,
                status alpha_status DEFAULT 'none',
                simulation_id TEXT,
                result JSONB,
                error TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                simulate_at TIMESTAMP,
                last_check TIMESTAMP,
                result_at TIMESTAMP
            );
        """))
        connection.execute(text("""
            CREATE TABLE IF NOT EXISTS alpha_reference (
                id SERIAL PRIMARY KEY,
                alpha_id INT NOT NULL REFERENCES alpha(id) ON DELETE CASCADE,
                template_id INT NOT NULL REFERENCES template(id) ON DELETE CASCADE
            );
        """))
        connection.execute(text("CREATE INDEX IF NOT EXISTS idx_alpha_reference_alpha_id ON alpha_reference(alpha_id);"))
        connection.execute(text("CREATE INDEX IF NOT EXISTS idx_alpha_reference_template_id ON alpha_reference(template_id);"))
    logger.info("Database schema initialized.")
